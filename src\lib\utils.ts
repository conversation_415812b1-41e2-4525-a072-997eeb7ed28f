/* eslint-disable @typescript-eslint/no-explicit-any */
import { SearchFilters } from "@/types";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

// Utility pour combiner les classes CSS
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Formatage des dates
export function formatDate(
  date: Date | string,
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
    ...options,
  };

  return new Intl.DateTimeFormat("fr-FR", defaultOptions).format(dateObj);
}

// Formatage des dates relatives
export function formatRelativeDate(date: Date | string): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  const now = new Date();
  const diffInMs = dateObj.getTime() - now.getTime();
  const diffInDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) return "Aujourd'hui";
  if (diffInDays === 1) return "Demain";
  if (diffInDays === -1) return "Hier";
  if (diffInDays > 1) return `Dans ${diffInDays} jours`;
  if (diffInDays < -1) return `Il y a ${Math.abs(diffInDays)} jours`;

  return formatDate(dateObj);
}

// Formatage des prix
export function formatPrice(price: number, currency: string = "EUR"): string {
  return new Intl.NumberFormat("fr-FR", {
    style: "currency",
    currency,
  }).format(price);
}

// Formatage des nombres
export function formatNumber(number: number): string {
  return new Intl.NumberFormat("fr-FR").format(number);
}

// Génération de slug à partir d'un titre
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "") // Supprime les accents
    .replace(/[^a-z0-9\s-]/g, "") // Supprime les caractères spéciaux
    .replace(/\s+/g, "-") // Remplace les espaces par des tirets
    .replace(/-+/g, "-") // Supprime les tirets multiples
    .trim();
}

// Validation d'email
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validation d'URL
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Calcul de la distance entre deux points géographiques
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Rayon de la Terre en km
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// Debounce function pour les recherches
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function pour les événements de scroll
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Capitalisation de la première lettre
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// Troncature de texte avec ellipses
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + "...";
}

// Génération d'ID unique
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Validation des filtres de recherche
export function validateSearchFilters(
  filters: Partial<SearchFilters>
): SearchFilters {
  const validatedFilters: SearchFilters = {};

  if (filters.query && filters.query.trim()) {
    validatedFilters.query = filters.query.trim();
  }

  if (filters.type && Array.isArray(filters.type) && filters.type.length > 0) {
    validatedFilters.type = filters.type;
  }

  if (filters.location) {
    validatedFilters.location = {};
    if (filters.location.city)
      validatedFilters.location.city = filters.location.city;
    if (filters.location.region)
      validatedFilters.location.region = filters.location.region;
    if (filters.location.radius && filters.location.radius > 0) {
      validatedFilters.location.radius = Math.min(filters.location.radius, 200);
    }
  }

  if (filters.dateRange) {
    validatedFilters.dateRange = {};
    if (filters.dateRange.start)
      validatedFilters.dateRange.start = filters.dateRange.start;
    if (filters.dateRange.end)
      validatedFilters.dateRange.end = filters.dateRange.end;
  }

  if (
    filters.domain &&
    Array.isArray(filters.domain) &&
    filters.domain.length > 0
  ) {
    validatedFilters.domain = filters.domain;
  }

  if (
    filters.level &&
    Array.isArray(filters.level) &&
    filters.level.length > 0
  ) {
    validatedFilters.level = filters.level;
  }

  if (typeof filters.isRemote === "boolean") {
    validatedFilters.isRemote = filters.isRemote;
  }

  if (typeof filters.isFree === "boolean") {
    validatedFilters.isFree = filters.isFree;
  }

  if (filters.sortBy) {
    validatedFilters.sortBy = filters.sortBy;
  }

  if (filters.sortOrder) {
    validatedFilters.sortOrder = filters.sortOrder;
  }

  return validatedFilters;
}
