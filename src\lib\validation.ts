/* eslint-disable @typescript-eslint/no-explicit-any */
import { Formation, Stage, SalonEmploi, Evenement, Location } from "@/types";
import { DOMAINS, EDUCATION_LEVELS, FRENCH_REGIONS } from "@/constants";

// Types pour les erreurs de validation
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Validation de base
export function validateRequired(
  value: any,
  fieldName: string
): ValidationError | null {
  if (value === undefined || value === null || value === "") {
    return { field: fieldName, message: `${fieldName} est requis` };
  }
  return null;
}

export function validateEmail(email: string): ValidationError | null {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { field: "email", message: "Format d'email invalide" };
  }
  return null;
}

export function validateUrl(
  url: string,
  fieldName: string
): ValidationError | null {
  try {
    new URL(url);
    return null;
  } catch {
    return { field: fieldName, message: "URL invalide" };
  }
}

export function validateDate(
  date: Date | string,
  fieldName: string
): ValidationError | null {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) {
    return { field: fieldName, message: "Date invalide" };
  }
  return null;
}

export function validateFutureDate(
  date: Date | string,
  fieldName: string
): ValidationError | null {
  const dateError = validateDate(date, fieldName);
  if (dateError) return dateError;

  const dateObj = typeof date === "string" ? new Date(date) : date;
  if (dateObj <= new Date()) {
    return { field: fieldName, message: "La date doit être dans le futur" };
  }
  return null;
}

// Validation de localisation
export function validateLocation(
  location: Partial<Location>
): ValidationError[] {
  const errors: ValidationError[] = [];

  const requiredError = validateRequired(location.address, "address");
  if (requiredError) errors.push(requiredError);

  const cityError = validateRequired(location.city, "city");
  if (cityError) errors.push(cityError);

  const postalCodeError = validateRequired(location.postalCode, "postalCode");
  if (postalCodeError) errors.push(postalCodeError);

  // Validation du code postal français
  if (location.postalCode && !/^\d{5}$/.test(location.postalCode)) {
    errors.push({
      field: "postalCode",
      message: "Code postal invalide (5 chiffres requis)",
    });
  }

  // Validation de la région
  if (location.region && !FRENCH_REGIONS.includes(location.region as any)) {
    errors.push({ field: "region", message: "Région invalide" });
  }

  return errors;
}

// Validation des formations
export function validateFormation(
  formation: Partial<Formation>
): ValidationResult {
  const errors: ValidationError[] = [];

  // Champs requis
  const titleError = validateRequired(formation.title, "title");
  if (titleError) errors.push(titleError);

  const descriptionError = validateRequired(
    formation.description,
    "description"
  );
  if (descriptionError) errors.push(descriptionError);

  const institutionError = validateRequired(
    formation.institution,
    "institution"
  );
  if (institutionError) errors.push(institutionError);

  // Validation des dates
  if (formation.startDate) {
    const startDateError = validateFutureDate(formation.startDate, "startDate");
    if (startDateError) errors.push(startDateError);
  }

  if (formation.endDate && formation.startDate) {
    const endDateError = validateDate(formation.endDate, "endDate");
    if (endDateError) errors.push(endDateError);

    if (formation.endDate <= formation.startDate) {
      errors.push({
        field: "endDate",
        message: "La date de fin doit être après la date de début",
      });
    }
  }

  // Validation de la localisation
  if (formation.location) {
    errors.push(...validateLocation(formation.location));
  }

  // Validation du domaine
  if (formation.domain && !DOMAINS.includes(formation.domain as any)) {
    errors.push({ field: "domain", message: "Domaine invalide" });
  }

  // Validation du niveau
  if (
    formation.level &&
    !EDUCATION_LEVELS.some((level) => level.value === formation.level)
  ) {
    errors.push({ field: "level", message: "Niveau d'études invalide" });
  }

  // Validation du coût
  if (formation.cost !== undefined && formation.cost < 0) {
    errors.push({ field: "cost", message: "Le coût ne peut pas être négatif" });
  }

  // Validation des URLs
  if (formation.website) {
    const websiteError = validateUrl(formation.website, "website");
    if (websiteError) errors.push(websiteError);
  }

  // Validation de l'email
  if (formation.contactEmail) {
    const emailError = validateEmail(formation.contactEmail);
    if (emailError) errors.push(emailError);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validation des stages
export function validateStage(stage: Partial<Stage>): ValidationResult {
  const errors: ValidationError[] = [];

  // Champs requis
  const titleError = validateRequired(stage.title, "title");
  if (titleError) errors.push(titleError);

  const companyError = validateRequired(stage.company, "company");
  if (companyError) errors.push(companyError);

  const positionError = validateRequired(stage.position, "position");
  if (positionError) errors.push(positionError);

  // Validation des dates
  if (stage.startDate) {
    const startDateError = validateDate(stage.startDate, "startDate");
    if (startDateError) errors.push(startDateError);
  }

  if (stage.applicationDeadline) {
    const deadlineError = validateFutureDate(
      stage.applicationDeadline,
      "applicationDeadline"
    );
    if (deadlineError) errors.push(deadlineError);
  }

  // Validation de la localisation
  if (stage.location) {
    errors.push(...validateLocation(stage.location));
  }

  // Validation du salaire
  if (stage.salary !== undefined && stage.salary < 0) {
    errors.push({
      field: "salary",
      message: "Le salaire ne peut pas être négatif",
    });
  }

  // Validation des URLs
  if (stage.website) {
    const websiteError = validateUrl(stage.website, "website");
    if (websiteError) errors.push(websiteError);
  }

  if (stage.applicationUrl) {
    const appUrlError = validateUrl(stage.applicationUrl, "applicationUrl");
    if (appUrlError) errors.push(appUrlError);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validation des salons d'emploi
export function validateSalonEmploi(
  salon: Partial<SalonEmploi>
): ValidationResult {
  const errors: ValidationError[] = [];

  // Champs requis
  const titleError = validateRequired(salon.title, "title");
  if (titleError) errors.push(titleError);

  const organizerError = validateRequired(salon.organizer, "organizer");
  if (organizerError) errors.push(organizerError);

  // Validation des dates
  if (salon.startDate) {
    const startDateError = validateFutureDate(salon.startDate, "startDate");
    if (startDateError) errors.push(startDateError);
  }

  if (salon.endDate && salon.startDate) {
    const endDateError = validateDate(salon.endDate, "endDate");
    if (endDateError) errors.push(endDateError);

    if (salon.endDate <= salon.startDate) {
      errors.push({
        field: "endDate",
        message: "La date de fin doit être après la date de début",
      });
    }
  }

  // Validation de la localisation
  if (salon.location) {
    errors.push(...validateLocation(salon.location));
  }

  // Validation du nombre d'entreprises
  if (salon.expectedCompanies !== undefined && salon.expectedCompanies < 0) {
    errors.push({
      field: "expectedCompanies",
      message: "Le nombre d'entreprises ne peut pas être négatif",
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validation des événements
export function validateEvenement(
  evenement: Partial<Evenement>
): ValidationResult {
  const errors: ValidationError[] = [];

  // Champs requis
  const titleError = validateRequired(evenement.title, "title");
  if (titleError) errors.push(titleError);

  const organizerError = validateRequired(evenement.organizer, "organizer");
  if (organizerError) errors.push(organizerError);

  // Validation des dates
  if (evenement.startDate) {
    const startDateError = validateFutureDate(evenement.startDate, "startDate");
    if (startDateError) errors.push(startDateError);
  }

  // Validation de la capacité
  if (evenement.capacity !== undefined && evenement.capacity < 0) {
    errors.push({
      field: "capacity",
      message: "La capacité ne peut pas être négative",
    });
  }

  // Validation du prix
  if (evenement.price !== undefined && evenement.price < 0) {
    errors.push({
      field: "price",
      message: "Le prix ne peut pas être négatif",
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
