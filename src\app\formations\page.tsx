"use client";

import React from "react";
import { GraduationCap, TrendingUp, Users, Clock } from "lucide-react";
import { Layout } from "@/components/layout/Layout";
import { ContentCard } from "@/components/common/ContentCard";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { mockFormations } from "@/data/mockData";
import { useFavorites } from "@/hooks/useLocalStorage";
import Link from "next/link";

export default function FormationsPage() {
  const { value: favorites, setValue: setFavorites } = useFavorites();

  const handleFavoriteToggle = (id: string) => {
    const newFavorites = favorites.includes(id)
      ? favorites.filter((fav) => fav !== id)
      : [...favorites, id];
    setFavorites(newFavorites);
  };

  // Statistiques des formations
  const stats = [
    {
      title: "Formations disponibles",
      value: "2,500+",
      icon: GraduationCap,
      description: "Dans tous les domaines",
    },
    {
      title: "Taux de réussite",
      value: "94%",
      icon: TrendingUp,
      description: "De nos étudiants",
    },
    {
      title: "Étudiants formés",
      value: "50,000+",
      icon: Users,
      description: "Chaque année",
    },
    {
      title: "Durée moyenne",
      value: "18 mois",
      icon: Clock,
      description: "Par formation",
    },
  ];

  // Catégories populaires
  const popularCategories = [
    { name: "Informatique", count: 450, color: "bg-blue-100 text-blue-800" },
    { name: "Commerce", count: 320, color: "bg-green-100 text-green-800" },
    { name: "Santé", count: 280, color: "bg-red-100 text-red-800" },
    { name: "Ingénierie", count: 240, color: "bg-purple-100 text-purple-800" },
    { name: "Design", count: 180, color: "bg-pink-100 text-pink-800" },
    { name: "Marketing", count: 160, color: "bg-orange-100 text-orange-800" },
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="gradient-bg-hero py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-primary/10 rounded-full">
                <GraduationCap className="h-12 w-12 text-primary" />
              </div>
            </div>
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Formations et Certifications
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Découvrez les meilleures formations pour développer vos
              compétences et booster votre carrière. Du niveau Bac au Bac+5,
              trouvez la formation qui vous correspond.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/search?type=formation">
                <Button size="lg">Explorer toutes les formations</Button>
              </Link>
              <Button variant="outline" size="lg">
                Conseils d&apos;orientation
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Statistiques */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center card-elevated">
                <CardContent className="p-6">
                  <stat.icon className="h-10 w-10 mx-auto mb-4 text-primary" />
                  <div className="text-2xl font-bold mb-1">{stat.value}</div>
                  <div className="font-medium mb-1">{stat.title}</div>
                  <div className="text-sm text-muted-foreground">
                    {stat.description}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Catégories populaires */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Domaines populaires</h2>
            <p className="text-xl text-muted-foreground">
              Explorez les formations par domaine d&apos;expertise
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {popularCategories.map((category, index) => (
              <Link
                key={index}
                href={`/search?type=formation&domain=${encodeURIComponent(
                  category.name
                )}`}
              >
                <Card className="card-interactive text-center h-full">
                  <CardContent className="p-4">
                    <Badge className={`mb-2 ${category.color}`}>
                      {category.count} formations
                    </Badge>
                    <h3 className="font-semibold">{category.name}</h3>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Formations en vedette */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold mb-4">Formations en vedette</h2>
              <p className="text-xl text-muted-foreground">
                Sélection de formations de qualité recommandées par nos experts
              </p>
            </div>
            <Link href="/search?type=formation">
              <Button variant="outline">Voir toutes les formations</Button>
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockFormations.map((formation) => (
              <ContentCard
                key={formation.id}
                item={formation}
                variant="featured"
                onFavoriteToggle={handleFavoriteToggle}
                isFavorite={favorites.includes(formation.id)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 gradient-bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Prêt à commencer votre formation ?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Rejoignez des milliers d&apos;étudiants qui ont déjà transformé leur
            carrière grâce à nos formations certifiantes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary">
              Créer mon profil
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
            >
              Parler à un conseiller
            </Button>
          </div>
        </div>
      </section>
    </Layout>
  );
}
