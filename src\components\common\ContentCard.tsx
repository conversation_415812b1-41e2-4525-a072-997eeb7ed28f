/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import Link from "next/link";
import { Calendar, MapPin, Clock, Euro, Users, Building } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ContentItem, ContentType } from "@/types";
import { formatDate, formatPrice, truncateText } from "@/lib/utils";

interface ContentCardProps {
  item: ContentItem;
  variant?: "default" | "compact" | "featured";
  showActions?: boolean;
  onFavoriteToggle?: (id: string) => void;
  isFavorite?: boolean;
}

export function ContentCard({
  item,
  variant = "default",
  showActions = true,
  onFavoriteToggle,
  isFavorite = false,
}: ContentCardProps) {
  const getContentType = (): ContentType => {
    if ("type" in item && item.type) return "formation";
    if ("company" in item) return "stage";
    if ("organizer" in item && "expectedCompanies" in item) return "salon";
    return "evenement";
  };

  const contentType = getContentType();

  const getBadgeVariant = () => {
    switch (contentType) {
      case "formation":
        return "formation";
      case "stage":
        return "stage";
      case "salon":
        return "salon";
      case "evenement":
        return "evenement";
      default:
        return "default";
    }
  };

  const getTypeLabel = () => {
    switch (contentType) {
      case "formation":
        return "Formation";
      case "stage":
        return "Stage";
      case "salon":
        return "Salon d'emploi";
      case "evenement":
        return "Événement";
      default:
        return "Contenu";
    }
  };

  const getMainInfo = () => {
    switch (contentType) {
      case "formation":
        const formation = item as any;
        return {
          subtitle: formation.institution,
          icon: <Building className="h-4 w-4" />,
          details: [
            {
              icon: <Calendar className="h-4 w-4" />,
              text: formatDate(formation.startDate),
            },
            {
              icon: <MapPin className="h-4 w-4" />,
              text: formation.location.city,
            },
            { icon: <Clock className="h-4 w-4" />, text: formation.duration },
            ...(formation.cost
              ? [
                  {
                    icon: <Euro className="h-4 w-4" />,
                    text: formatPrice(formation.cost),
                  },
                ]
              : []),
          ],
        };
      case "stage":
        const stage = item as any;
        return {
          subtitle: stage.company,
          icon: <Building className="h-4 w-4" />,
          details: [
            {
              icon: <Calendar className="h-4 w-4" />,
              text: formatDate(stage.startDate),
            },
            { icon: <MapPin className="h-4 w-4" />, text: stage.location.city },
            { icon: <Clock className="h-4 w-4" />, text: stage.duration },
            ...(stage.salary
              ? [
                  {
                    icon: <Euro className="h-4 w-4" />,
                    text: formatPrice(stage.salary),
                  },
                ]
              : []),
          ],
        };
      case "salon":
        const salon = item as any;
        return {
          subtitle: salon.organizer,
          icon: <Users className="h-4 w-4" />,
          details: [
            {
              icon: <Calendar className="h-4 w-4" />,
              text: formatDate(salon.startDate),
            },
            { icon: <MapPin className="h-4 w-4" />, text: salon.location.city },
            {
              icon: <Building className="h-4 w-4" />,
              text: `${salon.expectedCompanies} entreprises`,
            },
          ],
        };
      case "evenement":
        const evenement = item as any;
        return {
          subtitle: evenement.organizer,
          icon: <Users className="h-4 w-4" />,
          details: [
            {
              icon: <Calendar className="h-4 w-4" />,
              text: formatDate(evenement.startDate),
            },
            {
              icon: <MapPin className="h-4 w-4" />,
              text: evenement.location.city,
            },
            ...(evenement.capacity
              ? [
                  {
                    icon: <Users className="h-4 w-4" />,
                    text: `${evenement.capacity} places`,
                  },
                ]
              : []),
          ],
        };
      default:
        return {
          subtitle: "",
          icon: null,
          details: [],
        };
    }
  };

  const { subtitle, icon, details } = getMainInfo();
  const href = `/${contentType}s/${item.id}`;

  if (variant === "compact") {
    return (
      <Card className="card-interactive">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-2">
            <Badge variant={getBadgeVariant() as any} className="text-xs">
              {getTypeLabel()}
            </Badge>
            {showActions && onFavoriteToggle && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  onFavoriteToggle(item.id);
                }}
                className="h-6 w-6 p-0"
              >
                <span
                  className={
                    isFavorite ? "text-red-500" : "text-muted-foreground"
                  }
                >
                  ♥
                </span>
              </Button>
            )}
          </div>

          <Link href={href}>
            <h3 className="font-semibold text-sm mb-1 hover:text-primary transition-colors">
              {truncateText(item.title, 60)}
            </h3>
            <p className="text-xs text-muted-foreground mb-2">{subtitle}</p>
            <div className="flex items-center text-xs text-muted-foreground">
              <MapPin className="h-3 w-3 mr-1" />
              {item.location.city}
            </div>
          </Link>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={
        variant === "featured"
          ? "card-elevated border-primary/20"
          : "card-interactive"
      }
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between mb-2">
          <Badge variant={getBadgeVariant() as any}>{getTypeLabel()}</Badge>
          {showActions && onFavoriteToggle && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                onFavoriteToggle(item.id);
              }}
              className="h-8 w-8 p-0"
            >
              <span
                className={
                  isFavorite ? "text-red-500" : "text-muted-foreground"
                }
              >
                ♥
              </span>
            </Button>
          )}
        </div>

        <Link href={href}>
          <CardTitle className="text-lg hover:text-primary transition-colors cursor-pointer">
            {item.title}
          </CardTitle>
        </Link>

        {subtitle && (
          <div className="flex items-center text-muted-foreground">
            {icon}
            <span className="ml-2 text-sm">{subtitle}</span>
          </div>
        )}
      </CardHeader>

      <CardContent className="pb-3">
        <p className="text-sm text-muted-foreground mb-4">
          {truncateText(item.description, 120)}
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {details.slice(0, 4).map((detail, index) => (
            <div
              key={index}
              className="flex items-center text-sm text-muted-foreground"
            >
              {detail.icon}
              <span className="ml-2">{detail.text}</span>
            </div>
          ))}
        </div>
      </CardContent>

      {showActions && (
        <CardFooter className="pt-0">
          <div className="flex items-center justify-between w-full">
            <Link href={href}>
              <Button variant="outline" size="sm">
                Voir les détails
              </Button>
            </Link>

            <div className="text-xs text-muted-foreground">
              Mis à jour{" "}
              {formatDate(item.updatedAt, { month: "short", day: "numeric" })}
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
