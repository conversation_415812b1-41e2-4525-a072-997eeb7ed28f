// Constants pour la plateforme d'agrégation

// Domaines d'études et secteurs
export const DOMAINS = [
  'Informatique',
  'Ingénierie',
  'Commerce',
  'Marketing',
  'Finance',
  'Ressources Humaines',
  'Santé',
  'Éducation',
  'Droit',
  'Communication',
  'Design',
  'Architecture',
  'Environnement',
  'Agriculture',
  'Tourisme',
  'Transport',
  'Énergie',
  'Industrie',
  'Recherche',
  'Arts',
] as const;

// Niveaux d'études
export const EDUCATION_LEVELS = [
  { value: 'bac', label: 'Bac' },
  { value: 'bac+2', label: 'Bac+2' },
  { value: 'bac+3', label: 'Bac+3' },
  { value: 'bac+5', label: 'Bac+5' },
  { value: 'autre', label: 'Autre' },
] as const;

// Types de formations
export const FORMATION_TYPES = [
  { value: 'formation', label: 'Formation' },
  { value: 'certification', label: 'Certification' },
  { value: 'diplome', label: 'Diplôme' },
] as const;

// Types d'événements
export const EVENT_TYPES = [
  { value: 'conference', label: 'Conférence' },
  { value: 'workshop', label: 'Atelier' },
  { value: 'webinar', label: 'Webinaire' },
  { value: 'networking', label: 'Networking' },
  { value: 'autre', label: 'Autre' },
] as const;

// Régions françaises
export const FRENCH_REGIONS = [
  'Auvergne-Rhône-Alpes',
  'Bourgogne-Franche-Comté',
  'Bretagne',
  'Centre-Val de Loire',
  'Corse',
  'Grand Est',
  'Hauts-de-France',
  'Île-de-France',
  'Normandie',
  'Nouvelle-Aquitaine',
  'Occitanie',
  'Pays de la Loire',
  'Provence-Alpes-Côte d\'Azur',
  'Outre-mer',
] as const;

// Options de tri
export const SORT_OPTIONS = [
  { value: 'date', label: 'Date' },
  { value: 'relevance', label: 'Pertinence' },
  { value: 'location', label: 'Localisation' },
  { value: 'title', label: 'Titre' },
] as const;

// Configuration de pagination
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 12,
  MAX_PAGE_SIZE: 50,
  SEARCH_DEBOUNCE_MS: 300,
} as const;

// URLs et endpoints
export const API_ENDPOINTS = {
  FORMATIONS: '/api/formations',
  STAGES: '/api/stages',
  SALONS: '/api/salons',
  EVENEMENTS: '/api/evenements',
  SEARCH: '/api/search',
  STATS: '/api/stats',
} as const;

// Messages d'erreur
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Erreur de connexion. Veuillez réessayer.',
  NOT_FOUND: 'Aucun résultat trouvé.',
  INVALID_DATA: 'Données invalides.',
  SERVER_ERROR: 'Erreur serveur. Veuillez réessayer plus tard.',
  VALIDATION_ERROR: 'Veuillez vérifier les informations saisies.',
} as const;

// Configuration du site
export const SITE_CONFIG = {
  name: 'EduConnect France',
  description: 'Plateforme d\'agrégation d\'informations pour étudiants, alternants et chercheurs d\'emploi',
  url: 'https://educonnect-france.fr',
  ogImage: '/og-image.jpg',
  links: {
    twitter: 'https://twitter.com/educonnectfr',
    github: 'https://github.com/educonnect-france',
    linkedin: 'https://linkedin.com/company/educonnect-france',
  },
} as const;

// Navigation principale
export const MAIN_NAVIGATION = [
  {
    label: 'Formations',
    href: '/formations',
    icon: 'GraduationCap',
  },
  {
    label: 'Stages',
    href: '/stages',
    icon: 'Briefcase',
  },
  {
    label: 'Salons d\'emploi',
    href: '/salons',
    icon: 'Users',
  },
  {
    label: 'Événements',
    href: '/evenements',
    icon: 'Calendar',
  },
] as const;

// Durées communes
export const COMMON_DURATIONS = [
  '1 semaine',
  '2 semaines',
  '1 mois',
  '2 mois',
  '3 mois',
  '6 mois',
  '1 an',
  '2 ans',
  '3 ans',
] as const;

// Compétences populaires
export const POPULAR_SKILLS = [
  'JavaScript',
  'Python',
  'React',
  'Node.js',
  'SQL',
  'Git',
  'Agile',
  'Scrum',
  'Communication',
  'Gestion de projet',
  'Analyse de données',
  'Marketing digital',
  'Design UX/UI',
  'Comptabilité',
  'Négociation',
] as const;
