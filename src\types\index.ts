// Types de base pour la plateforme d'agrégation
export interface BaseEntity {
  id: string;
  title: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// Types pour les formations
export interface Formation extends BaseEntity {
  type: 'formation' | 'certification' | 'diplome';
  level: 'bac' | 'bac+2' | 'bac+3' | 'bac+5' | 'autre';
  domain: string;
  institution: string;
  location: Location;
  duration: string;
  startDate: Date;
  endDate?: Date;
  applicationDeadline?: Date;
  cost?: number;
  isOnline: boolean;
  prerequisites?: string[];
  skills: string[];
  website?: string;
  contactEmail?: string;
  contactPhone?: string;
}

// Types pour les stages
export interface Stage extends BaseEntity {
  company: string;
  position: string;
  domain: string;
  location: Location;
  duration: string;
  startDate: Date;
  endDate?: Date;
  applicationDeadline?: Date;
  salary?: number;
  level: 'bac' | 'bac+2' | 'bac+3' | 'bac+5' | 'tous';
  isRemote: boolean;
  skills: string[];
  benefits?: string[];
  website?: string;
  contactEmail?: string;
  applicationUrl?: string;
}

// Types pour les salons d'emploi
export interface SalonEmploi extends BaseEntity {
  organizer: string;
  location: Location;
  startDate: Date;
  endDate: Date;
  registrationDeadline?: Date;
  targetAudience: string[];
  sectors: string[];
  expectedCompanies: number;
  isVirtual: boolean;
  registrationFee?: number;
  website?: string;
  registrationUrl?: string;
}

// Types pour les événements
export interface Evenement extends BaseEntity {
  type: 'conference' | 'workshop' | 'webinar' | 'networking' | 'autre';
  organizer: string;
  location: Location;
  startDate: Date;
  endDate?: Date;
  registrationDeadline?: Date;
  targetAudience: string[];
  topics: string[];
  speakers?: Speaker[];
  capacity?: number;
  isVirtual: boolean;
  isFree: boolean;
  price?: number;
  website?: string;
  registrationUrl?: string;
}

// Types auxiliaires
export interface Location {
  address: string;
  city: string;
  postalCode: string;
  region: string;
  country: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface Speaker {
  name: string;
  title: string;
  company?: string;
  bio?: string;
  photo?: string;
}

// Types pour les filtres de recherche
export interface SearchFilters {
  query?: string;
  type?: ContentType[];
  location?: {
    city?: string;
    region?: string;
    radius?: number; // en km
  };
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  domain?: string[];
  level?: string[];
  isRemote?: boolean;
  isFree?: boolean;
  sortBy?: 'date' | 'relevance' | 'location' | 'title';
  sortOrder?: 'asc' | 'desc';
}

// Types pour les résultats de recherche
export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// Union types
export type ContentType = 'formation' | 'stage' | 'salon' | 'evenement';
export type ContentItem = Formation | Stage | SalonEmploi | Evenement;

// Types pour l'API
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Types pour la navigation
export interface NavigationItem {
  label: string;
  href: string;
  icon?: string;
  children?: NavigationItem[];
}

// Types pour les statistiques
export interface PlatformStats {
  totalFormations: number;
  totalStages: number;
  totalSalons: number;
  totalEvenements: number;
  activeUsers: number;
  lastUpdate: Date;
}
