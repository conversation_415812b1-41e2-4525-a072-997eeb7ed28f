@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;

  /* Couleurs principales - Palette éducative moderne */
  --background: oklch(0.99 0.005 240);
  --foreground: oklch(0.15 0.02 240);

  /* Cartes et surfaces */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 240);

  /* Couleur primaire - Bleu éducatif moderne */
  --primary: oklch(0.55 0.15 240);
  --primary-foreground: oklch(0.98 0.01 240);

  /* Couleur secondaire - Vert croissance */
  --secondary: oklch(0.92 0.02 160);
  --secondary-foreground: oklch(0.25 0.05 160);

  /* Couleurs neutres */
  --muted: oklch(0.96 0.01 240);
  --muted-foreground: oklch(0.45 0.03 240);
  --accent: oklch(0.94 0.03 200);
  --accent-foreground: oklch(0.25 0.05 200);

  /* États et interactions */
  --destructive: oklch(0.65 0.2 25);
  --destructive-foreground: oklch(0.98 0.01 25);
  --success: oklch(0.6 0.15 140);
  --success-foreground: oklch(0.98 0.01 140);
  --warning: oklch(0.75 0.15 60);
  --warning-foreground: oklch(0.15 0.02 60);

  /* Bordures et inputs */
  --border: oklch(0.9 0.01 240);
  --input: oklch(0.94 0.01 240);
  --ring: oklch(0.55 0.15 240);

  /* Graphiques et données */
  --chart-1: oklch(0.6 0.2 240);
  --chart-2: oklch(0.65 0.18 160);
  --chart-3: oklch(0.7 0.16 300);
  --chart-4: oklch(0.75 0.14 60);
  --chart-5: oklch(0.8 0.12 20);

  /* Sidebar */
  --sidebar: oklch(0.97 0.01 240);
  --sidebar-foreground: oklch(0.2 0.02 240);
  --sidebar-primary: oklch(0.55 0.15 240);
  --sidebar-primary-foreground: oklch(0.98 0.01 240);
  --sidebar-accent: oklch(0.94 0.02 240);
  --sidebar-accent-foreground: oklch(0.25 0.03 240);
  --sidebar-border: oklch(0.9 0.01 240);
  --sidebar-ring: oklch(0.55 0.15 240);

  /* Couleurs spécifiques à la plateforme */
  --formation: oklch(0.6 0.15 240);
  --stage: oklch(0.65 0.18 160);
  --salon: oklch(0.7 0.16 300);
  --evenement: oklch(0.75 0.14 60);
}

.dark {
  /* Couleurs principales - Mode sombre élégant */
  --background: oklch(0.08 0.01 240);
  --foreground: oklch(0.95 0.01 240);

  /* Cartes et surfaces */
  --card: oklch(0.12 0.02 240);
  --card-foreground: oklch(0.95 0.01 240);
  --popover: oklch(0.12 0.02 240);
  --popover-foreground: oklch(0.95 0.01 240);

  /* Couleur primaire - Bleu lumineux pour le mode sombre */
  --primary: oklch(0.7 0.18 240);
  --primary-foreground: oklch(0.08 0.01 240);

  /* Couleur secondaire - Vert adapté au mode sombre */
  --secondary: oklch(0.2 0.03 160);
  --secondary-foreground: oklch(0.85 0.02 160);

  /* Couleurs neutres */
  --muted: oklch(0.15 0.02 240);
  --muted-foreground: oklch(0.65 0.03 240);
  --accent: oklch(0.18 0.03 200);
  --accent-foreground: oklch(0.85 0.02 200);

  /* États et interactions */
  --destructive: oklch(0.7 0.22 25);
  --destructive-foreground: oklch(0.95 0.01 25);
  --success: oklch(0.65 0.18 140);
  --success-foreground: oklch(0.08 0.01 140);
  --warning: oklch(0.8 0.18 60);
  --warning-foreground: oklch(0.08 0.01 60);

  /* Bordures et inputs */
  --border: oklch(0.2 0.02 240);
  --input: oklch(0.18 0.02 240);
  --ring: oklch(0.7 0.18 240);

  /* Graphiques et données - Mode sombre */
  --chart-1: oklch(0.65 0.22 240);
  --chart-2: oklch(0.7 0.2 160);
  --chart-3: oklch(0.75 0.18 300);
  --chart-4: oklch(0.8 0.16 60);
  --chart-5: oklch(0.85 0.14 20);

  /* Sidebar */
  --sidebar: oklch(0.1 0.02 240);
  --sidebar-foreground: oklch(0.9 0.01 240);
  --sidebar-primary: oklch(0.7 0.18 240);
  --sidebar-primary-foreground: oklch(0.08 0.01 240);
  --sidebar-accent: oklch(0.16 0.02 240);
  --sidebar-accent-foreground: oklch(0.85 0.02 240);
  --sidebar-border: oklch(0.2 0.02 240);
  --sidebar-ring: oklch(0.7 0.18 240);

  /* Couleurs spécifiques à la plateforme - Mode sombre */
  --formation: oklch(0.65 0.18 240);
  --stage: oklch(0.7 0.2 160);
  --salon: oklch(0.75 0.18 300);
  --evenement: oklch(0.8 0.16 60);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Amélioration de la lisibilité */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-xl lg:text-2xl;
  }

  /* Styles pour les liens */
  a {
    @apply transition-colors duration-200;
  }

  /* Focus visible amélioré */
  :focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }

  /* Scrollbar personnalisée */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Animations fluides */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Gradient backgrounds pour les sections */
  .gradient-bg-primary {
    background: linear-gradient(
      135deg,
      oklch(var(--primary)) 0%,
      oklch(var(--primary) / 0.8) 100%
    );
  }

  .gradient-bg-secondary {
    background: linear-gradient(
      135deg,
      oklch(var(--secondary)) 0%,
      oklch(var(--secondary) / 0.8) 100%
    );
  }

  .gradient-bg-hero {
    background: linear-gradient(
      135deg,
      oklch(var(--primary) / 0.1) 0%,
      oklch(var(--secondary) / 0.1) 50%,
      oklch(var(--accent) / 0.1) 100%
    );
  }

  /* Styles pour les cartes avec effet de profondeur */
  .card-elevated {
    @apply shadow-lg hover:shadow-xl transition-shadow duration-300;
  }

  .card-interactive {
    @apply hover:scale-[1.02] transition-transform duration-200 cursor-pointer;
  }

  /* Styles pour les badges de catégories */
  .badge-formation {
    @apply bg-[oklch(var(--formation)/0.1)] text-[oklch(var(--formation))] border-[oklch(var(--formation)/0.2)];
  }

  .badge-stage {
    @apply bg-[oklch(var(--stage)/0.1)] text-[oklch(var(--stage))] border-[oklch(var(--stage)/0.2)];
  }

  .badge-salon {
    @apply bg-[oklch(var(--salon)/0.1)] text-[oklch(var(--salon))] border-[oklch(var(--salon)/0.2)];
  }

  .badge-evenement {
    @apply bg-[oklch(var(--evenement)/0.1)] text-[oklch(var(--evenement))] border-[oklch(var(--evenement)/0.2)];
  }
}
