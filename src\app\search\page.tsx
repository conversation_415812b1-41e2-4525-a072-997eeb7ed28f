'use client'

import React from 'react'
import { useSearchParams } from 'next/navigation'
import { Search } from 'lucide-react'
import { Layout } from '@/components/layout/Layout'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { SearchFilters } from '@/components/search/SearchFilters'
import { SearchResults } from '@/components/search/SearchResults'
import { useSearch } from '@/hooks/useSearch'
import { useFavorites } from '@/hooks/useLocalStorage'

export default function SearchPage() {
  const searchParams = useSearchParams()
  const initialQuery = searchParams.get('q') || ''
  
  const [searchQuery, setSearchQuery] = React.useState(initialQuery)
  const [isFiltersCollapsed, setIsFiltersCollapsed] = React.useState(false)
  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid')
  
  const { value: favorites, setValue: setFavorites } = useFavorites()
  
  const {
    results,
    isLoading,
    error,
    filters,
    search,
    updateFilters,
    clearFilters,
    loadMore,
    hasMore
  } = useSearch({
    initialFilters: { query: initialQuery },
    autoSearch: true
  })

  const handleSearch = () => {
    search({ query: searchQuery })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const handleFavoriteToggle = (id: string) => {
    const newFavorites = favorites.includes(id)
      ? favorites.filter(fav => fav !== id)
      : [...favorites, id]
    setFavorites(newFavorites)
  }

  // Synchroniser la query avec l'état local
  React.useEffect(() => {
    if (initialQuery && initialQuery !== searchQuery) {
      setSearchQuery(initialQuery)
    }
  }, [initialQuery])

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Barre de recherche */}
        <div className="mb-8">
          <div className="max-w-2xl mx-auto">
            <div className="flex gap-2">
              <Input
                type="search"
                placeholder="Rechercher formations, stages, événements..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                startIcon={<Search className="h-5 w-5" />}
                className="text-lg h-12"
              />
              <Button 
                onClick={handleSearch}
                size="lg"
                className="h-12 px-6"
              >
                Rechercher
              </Button>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar des filtres */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-24">
              <SearchFilters
                filters={filters}
                onFiltersChange={updateFilters}
                onClearFilters={clearFilters}
                isCollapsed={isFiltersCollapsed}
                onToggleCollapse={() => setIsFiltersCollapsed(!isFiltersCollapsed)}
                className="lg:block"
              />
            </div>
          </div>

          {/* Zone des résultats */}
          <div className="lg:col-span-3">
            {/* Filtres mobiles */}
            <div className="lg:hidden mb-6">
              <SearchFilters
                filters={filters}
                onFiltersChange={updateFilters}
                onClearFilters={clearFilters}
                isCollapsed={true}
                onToggleCollapse={() => setIsFiltersCollapsed(!isFiltersCollapsed)}
              />
            </div>

            {/* Filtres mobiles expandés */}
            {isFiltersCollapsed && (
              <div className="lg:hidden mb-6">
                <SearchFilters
                  filters={filters}
                  onFiltersChange={updateFilters}
                  onClearFilters={clearFilters}
                  isCollapsed={false}
                  onToggleCollapse={() => setIsFiltersCollapsed(false)}
                />
              </div>
            )}

            {/* Résultats */}
            <SearchResults
              results={results}
              isLoading={isLoading}
              error={error}
              onLoadMore={hasMore ? loadMore : undefined}
              onFavoriteToggle={handleFavoriteToggle}
              favorites={favorites}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
          </div>
        </div>
      </div>
    </Layout>
  )
}
