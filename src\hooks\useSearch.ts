import { useState, useCallback, useEffect } from 'react'
import { SearchFilters, SearchResult, ContentItem } from '@/types'
import { debounce, validateSearchFilters } from '@/lib/utils'
import { PAGINATION_CONFIG } from '@/constants'

interface UseSearchOptions {
  initialFilters?: Partial<SearchFilters>
  debounceMs?: number
  autoSearch?: boolean
}

interface UseSearchReturn {
  // État
  results: SearchResult<ContentItem> | null
  isLoading: boolean
  error: string | null
  filters: SearchFilters
  
  // Actions
  search: (newFilters?: Partial<SearchFilters>) => Promise<void>
  updateFilters: (newFilters: Partial<SearchFilters>) => void
  clearFilters: () => void
  loadMore: () => Promise<void>
  
  // Helpers
  hasMore: boolean
  totalResults: number
}

export function useSearch(options: UseSearchOptions = {}): UseSearchReturn {
  const {
    initialFilters = {},
    debounceMs = PAGINATION_CONFIG.SEARCH_DEBOUNCE_MS,
    autoSearch = true
  } = options

  // État local
  const [results, setResults] = useState<SearchResult<ContentItem> | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<SearchFilters>(() => 
    validateSearchFilters(initialFilters)
  )

  // Fonction de recherche
  const performSearch = useCallback(async (
    searchFilters: SearchFilters,
    page: number = 1,
    append: boolean = false
  ) => {
    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: PAGINATION_CONFIG.DEFAULT_PAGE_SIZE.toString(),
        ...Object.entries(searchFilters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              acc[key] = value.join(',')
            } else if (typeof value === 'object') {
              acc[key] = JSON.stringify(value)
            } else {
              acc[key] = value.toString()
            }
          }
          return acc
        }, {} as Record<string, string>)
      })

      const response = await fetch(`/api/search?${params}`)
      
      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`)
      }

      const data: SearchResult<ContentItem> = await response.json()
      
      setResults(prevResults => {
        if (append && prevResults) {
          return {
            ...data,
            items: [...prevResults.items, ...data.items]
          }
        }
        return data
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur de recherche'
      setError(errorMessage)
      console.error('Erreur de recherche:', err)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Recherche avec debounce
  const debouncedSearch = useCallback(
    debounce((searchFilters: SearchFilters) => {
      performSearch(searchFilters)
    }, debounceMs),
    [performSearch, debounceMs]
  )

  // Fonction de recherche publique
  const search = useCallback(async (newFilters?: Partial<SearchFilters>) => {
    const updatedFilters = newFilters 
      ? validateSearchFilters({ ...filters, ...newFilters })
      : filters
    
    setFilters(updatedFilters)
    await performSearch(updatedFilters)
  }, [filters, performSearch])

  // Mise à jour des filtres
  const updateFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    const updatedFilters = validateSearchFilters({ ...filters, ...newFilters })
    setFilters(updatedFilters)
    
    if (autoSearch) {
      debouncedSearch(updatedFilters)
    }
  }, [filters, autoSearch, debouncedSearch])

  // Réinitialisation des filtres
  const clearFilters = useCallback(() => {
    const clearedFilters = validateSearchFilters({})
    setFilters(clearedFilters)
    setResults(null)
    setError(null)
  }, [])

  // Chargement de plus de résultats
  const loadMore = useCallback(async () => {
    if (!results || !results.hasMore || isLoading) return
    
    const nextPage = Math.floor(results.items.length / PAGINATION_CONFIG.DEFAULT_PAGE_SIZE) + 1
    await performSearch(filters, nextPage, true)
  }, [results, isLoading, filters, performSearch])

  // Recherche automatique au montage si des filtres sont définis
  useEffect(() => {
    if (autoSearch && Object.keys(filters).length > 0) {
      debouncedSearch(filters)
    }
  }, []) // Seulement au montage

  return {
    // État
    results,
    isLoading,
    error,
    filters,
    
    // Actions
    search,
    updateFilters,
    clearFilters,
    loadMore,
    
    // Helpers
    hasMore: results?.hasMore ?? false,
    totalResults: results?.total ?? 0
  }
}
