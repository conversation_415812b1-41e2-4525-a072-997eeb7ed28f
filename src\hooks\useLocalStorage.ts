import { useState, useEffect, useCallback } from "react";

type SetValue<T> = T | ((val: T) => T);

interface UseLocalStorageReturn<T> {
  value: T;
  setValue: (value: SetValue<T>) => void;
  removeValue: () => void;
}

/**
 * Hook personnalisé pour gérer le localStorage avec TypeScript
 * Gère automatiquement la sérialisation/désérialisation JSON
 * Inclut la gestion d'erreurs et la synchronisation entre onglets
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): UseLocalStorageReturn<T> {
  // Fonction pour lire la valeur du localStorage
  const readValue = useCallback((): T => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(
        `Erreur lors de la lecture du localStorage pour la clé "${key}":`,
        error
      );
      return initialValue;
    }
  }, [initialValue, key]);

  // État avec la valeur du localStorage
  const [storedValue, setStoredValue] = useState<T>(readValue);

  // Fonction pour sauvegarder dans le localStorage
  const setValue = useCallback(
    (value: SetValue<T>) => {
      if (typeof window === "undefined") {
        console.warn("localStorage n'est pas disponible côté serveur");
        return;
      }

      try {
        // Permet de passer une fonction pour mettre à jour la valeur
        const newValue = value instanceof Function ? value(storedValue) : value;

        // Sauvegarde dans le localStorage
        window.localStorage.setItem(key, JSON.stringify(newValue));

        // Mise à jour de l'état local
        setStoredValue(newValue);

        // Déclenche un événement personnalisé pour synchroniser entre onglets
        window.dispatchEvent(
          new CustomEvent("local-storage", {
            detail: { key, newValue },
          })
        );
      } catch (error) {
        console.error(
          `Erreur lors de la sauvegarde dans localStorage pour la clé "${key}":`,
          error
        );
      }
    },
    [key, storedValue]
  );

  // Fonction pour supprimer la valeur du localStorage
  const removeValue = useCallback(() => {
    if (typeof window === "undefined") {
      console.warn("localStorage n'est pas disponible côté serveur");
      return;
    }

    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);

      // Déclenche un événement personnalisé
      window.dispatchEvent(
        new CustomEvent("local-storage", {
          detail: { key, newValue: initialValue },
        })
      );
    } catch (error) {
      console.error(
        `Erreur lors de la suppression du localStorage pour la clé "${key}":`,
        error
      );
    }
  }, [key, initialValue]);

  // Écoute les changements du localStorage (synchronisation entre onglets)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue));
        } catch (error) {
          console.warn(
            `Erreur lors de la synchronisation du localStorage pour la clé "${key}":`,
            error
          );
        }
      }
    };

    const handleCustomStorageChange = (e: CustomEvent) => {
      if (e.detail.key === key) {
        setStoredValue(e.detail.newValue);
      }
    };

    // Écoute les événements natifs du localStorage (changements d'autres onglets)
    window.addEventListener("storage", handleStorageChange);

    // Écoute les événements personnalisés (changements du même onglet)
    window.addEventListener(
      "local-storage",
      handleCustomStorageChange as EventListener
    );

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener(
        "local-storage",
        handleCustomStorageChange as EventListener
      );
    };
  }, [key]);

  return {
    value: storedValue,
    setValue,
    removeValue,
  };
}

/**
 * Hook spécialisé pour gérer les favoris
 */
export function useFavorites() {
  return useLocalStorage<string[]>("educonnect-favorites", []);
}

/**
 * Hook spécialisé pour gérer les préférences utilisateur
 */
export interface UserPreferences {
  theme: "light" | "dark" | "system";
  language: "fr" | "en";
  notifications: boolean;
  defaultLocation?: string;
  defaultRadius?: number;
}

export function useUserPreferences() {
  return useLocalStorage<UserPreferences>("educonnect-preferences", {
    theme: "system",
    language: "fr",
    notifications: true,
  });
}

/**
 * Hook spécialisé pour gérer l'historique de recherche
 */
export interface SearchHistoryItem {
  id: string;
  query: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  filters: Record<string, any>;
  timestamp: Date;
}

export function useSearchHistory() {
  const { value, setValue, removeValue } = useLocalStorage<SearchHistoryItem[]>(
    "educonnect-search-history",
    []
  );

  const addToHistory = useCallback(
    (item: Omit<SearchHistoryItem, "id" | "timestamp">) => {
      const newItem: SearchHistoryItem = {
        ...item,
        id: Math.random().toString(36).substr(2, 9),
        timestamp: new Date(),
      };

      setValue((prev) => {
        // Évite les doublons basés sur la requête
        const filtered = prev.filter(
          (historyItem) => historyItem.query !== item.query
        );
        // Garde seulement les 20 dernières recherches
        return [newItem, ...filtered].slice(0, 20);
      });
    },
    [setValue]
  );

  const clearHistory = useCallback(() => {
    removeValue();
  }, [removeValue]);

  return {
    history: value,
    addToHistory,
    clearHistory,
  };
}
