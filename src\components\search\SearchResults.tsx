"use client";

import React from "react";
import { Loader2, <PERSON>ert<PERSON><PERSON>cle, Grid, List } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ContentCard } from "@/components/common/ContentCard";
import { SearchResult, ContentItem } from "@/types";
import { cn } from "@/lib/utils";

interface SearchResultsProps {
  results: SearchResult<ContentItem> | null;
  isLoading: boolean;
  error: string | null;
  onLoadMore?: () => void;
  onFavoriteToggle?: (id: string) => void;
  favorites?: string[];
  viewMode?: "grid" | "list";
  onViewModeChange?: (mode: "grid" | "list") => void;
  className?: string;
}

export function SearchResults({
  results,
  isLoading,
  error,
  onLoadMore,
  onFavoriteToggle,
  favorites = [],
  viewMode = "grid",
  onViewModeChange,
  className,
}: SearchResultsProps) {
  // État de chargement initial
  if (isLoading && !results) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Recherche en cours...</p>
        </div>
      </div>
    );
  }

  // État d'erreur
  if (error) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <AlertCircle className="h-8 w-8 mx-auto mb-4 text-destructive" />
          <h3 className="font-semibold mb-2">Erreur de recherche</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    );
  }

  // Aucun résultat
  if (results && results.items.length === 0) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <div className="h-16 w-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="font-semibold mb-2">Aucun résultat trouvé</h3>
          <p className="text-muted-foreground mb-4">
            Essayez de modifier vos critères de recherche ou d&apos;élargir
            votre zone géographique.
          </p>
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <Button variant="outline">Modifier les filtres</Button>
            <Button variant="outline">Effacer la recherche</Button>
          </div>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <p className="text-muted-foreground">
            Utilisez la barre de recherche pour trouver des opportunités.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* En-tête des résultats */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {results.total.toLocaleString("fr-FR")} résultat
            {results.total > 1 ? "s" : ""}
          </h2>
          <p className="text-muted-foreground">
            Page {results.page} sur{" "}
            {Math.ceil(results.total / results.pageSize)}
          </p>
        </div>

        {/* Sélecteur de vue */}
        {onViewModeChange && (
          <div className="flex items-center gap-1 border rounded-md p-1">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => onViewModeChange("grid")}
              className="h-8 w-8 p-0"
            >
              <Grid className="h-4 w-4" />
              <span className="sr-only">Vue grille</span>
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => onViewModeChange("list")}
              className="h-8 w-8 p-0"
            >
              <List className="h-4 w-4" />
              <span className="sr-only">Vue liste</span>
            </Button>
          </div>
        )}
      </div>

      {/* Grille de résultats */}
      <div
        className={cn(
          "grid gap-6",
          viewMode === "grid"
            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
            : "grid-cols-1"
        )}
      >
        {results.items.map((item) => (
          <ContentCard
            key={item.id}
            item={item}
            variant={viewMode === "list" ? "compact" : "default"}
            onFavoriteToggle={onFavoriteToggle}
            isFavorite={favorites.includes(item.id)}
          />
        ))}
      </div>

      {/* Indicateur de chargement pour le chargement de plus de résultats */}
      {isLoading && results.items.length > 0 && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">Chargement...</span>
        </div>
      )}

      {/* Bouton "Charger plus" */}
      {results.hasMore && !isLoading && onLoadMore && (
        <div className="flex justify-center py-8">
          <Button onClick={onLoadMore} variant="outline" size="lg">
            Charger plus de résultats
          </Button>
        </div>
      )}

      {/* Indicateur de fin de résultats */}
      {!results.hasMore && results.items.length > 0 && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Vous avez vu tous les résultats disponibles.
          </p>
        </div>
      )}
    </div>
  );
}
