# EduConnect France 🎓

Une plateforme web moderne d'agrégation d'informations pour les étudiants, alternants et chercheurs d'emploi en France.

## 🎯 Objectif

Rendre accessibles les informations sur les formations, stages, salons d'emploi et événements importants disponibles en France aux élèves, étudiants, alternants et chercheurs d'emploi qui peinent souvent à avoir l'information au bon moment.

## ✨ Fonctionnalités

- **🔍 Recherche avancée** : Système de recherche intelligent avec filtres par localisation, domaine, type, dates
- **📚 Formations** : Catalogue complet des formations disponibles (diplômes, certifications, formations courtes)
- **💼 Stages** : Offres de stages dans tous les domaines
- **🏢 Salons d'emploi** : Événements de recrutement et networking
- **📅 Événements** : Conférences, ateliers, webinaires
- **❤️ Favoris** : Sauvegarde des opportunités intéressantes
- **📱 Responsive** : Interface optimisée pour tous les appareils
- **🌙 Mode sombre** : Thème adaptatif selon les préférences

## 🛠️ Technologies

- **Framework** : Next.js 15 (App Router)
- **Styling** : TailwindCSS v4
- **UI Components** : Shadcn/ui + Radix UI
- **Icons** : Lucide React
- **TypeScript** : Pour la robustesse du code
- **Animations** : CSS animations personnalisées

## 🏗️ Architecture

Le projet suit les principes SOLID et DRY avec une architecture modulaire :

```
src/
├── app/                    # Pages et routes (App Router)
├── components/
│   ├── ui/                # Composants UI de base (Shadcn)
│   ├── layout/            # Composants de mise en page
│   ├── search/            # Composants de recherche
│   └── common/            # Composants réutilisables
├── hooks/                 # Hooks personnalisés
├── lib/                   # Utilitaires et helpers
├── types/                 # Types TypeScript
├── constants/             # Constantes et configuration
└── data/                  # Données de démonstration
```

## 🚀 Installation et Démarrage

1. **Cloner le repository**

```bash
git clone <repository-url>
cd plateforme-web-aggregation
```

2. **Installer les dépendances**

```bash
pnpm install
```

3. **Lancer le serveur de développement**

```bash
pnpm dev
```

4. **Ouvrir dans le navigateur**
   Aller sur [http://localhost:3000](http://localhost:3000)

## 📝 Scripts Disponibles

- `pnpm dev` : Démarre le serveur de développement
- `pnpm build` : Build de production
- `pnpm start` : Démarre le serveur de production
- `pnpm lint` : Vérification du code avec ESLint

## 🎨 Système de Design

### Palette de Couleurs

Le thème utilise une palette moderne et professionnelle :

- **Primaire** : Bleu éducatif moderne
- **Secondaire** : Vert croissance
- **Accent** : Couleurs spécifiques par type de contenu
- **Neutres** : Gamme de gris pour la lisibilité

### Composants

- **Cards** : Avec variantes (default, elevated, interactive)
- **Badges** : Couleurs spécifiques par catégorie
- **Buttons** : Multiples variantes et tailles
- **Forms** : Inputs avec icônes et validation
- **Navigation** : Header responsive avec menu mobile

## 🔧 Fonctionnalités Techniques

### Performance

- **Optimisation des images** : WebP/AVIF avec lazy loading
- **Code splitting** : Chargement optimisé des composants
- **Caching** : Stratégies de cache pour l'API
- **Bundle optimization** : Optimisation des imports

### SEO

- **Métadonnées dynamiques** : Génération automatique
- **Structured Data** : Schema.org pour les contenus
- **Sitemap** : Génération automatique
- **Open Graph** : Partage optimisé sur les réseaux sociaux

### Accessibilité

- **ARIA labels** : Navigation accessible
- **Focus management** : Navigation au clavier
- **Contrast ratios** : Respect des standards WCAG
- **Screen readers** : Support complet

## 📊 Types de Données

### Formation

- Informations générales (titre, description, institution)
- Détails pratiques (dates, durée, coût, niveau)
- Localisation et modalités
- Compétences et prérequis

### Stage

- Informations de l'entreprise
- Détails du poste et missions
- Conditions (durée, rémunération, avantages)
- Modalités de candidature

### Salon d'Emploi

- Organisateur et lieu
- Secteurs représentés
- Nombre d'entreprises attendues
- Modalités d'inscription

### Événement

- Type (conférence, atelier, webinaire)
- Programme et intervenants
- Modalités de participation
- Tarification

## 🔍 Système de Recherche

### Filtres Disponibles

- **Type de contenu** : Formation, stage, salon, événement
- **Localisation** : Ville, région, rayon de recherche
- **Domaine** : Secteur d'activité
- **Niveau** : Niveau d'études requis
- **Options** : Télétravail, gratuit, etc.

### Tri

- Par pertinence
- Par date
- Par localisation
- Par titre (alphabétique)

## 🎯 Roadmap

### Phase 1 ✅

- [x] Architecture de base
- [x] Système de design
- [x] Composants UI
- [x] Page d'accueil
- [x] Système de recherche
- [x] Pages de contenu

### Phase 2 (À venir)

- [ ] Authentification utilisateur
- [ ] Profils personnalisés
- [ ] Notifications et alertes
- [ ] API backend réelle
- [ ] Base de données
- [ ] Système de recommandations

### Phase 3 (À venir)

- [ ] Application mobile
- [ ] Intégrations partenaires
- [ ] Analytics avancées
- [ ] Système de notation
- [ ] Chat et support

## 🤝 Contribution

Les contributions sont les bienvenues ! Merci de :

1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Contact

Pour toute question ou suggestion :

- Email : <EMAIL>
- Twitter : [@educonnectfr](https://twitter.com/educonnectfr)
- LinkedIn : [EduConnect France](https://linkedin.com/company/educonnect-france)

---

Développé avec ❤️ pour l'éducation et l'emploi en France
