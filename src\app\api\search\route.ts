import { NextRequest, NextResponse } from 'next/server'
import { getAllContent } from '@/data/mockData'
import { SearchFilters, SearchResult, ContentItem } from '@/types'
import { PAGINATION_CONFIG } from '@/constants'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extraction des paramètres de recherche
    const query = searchParams.get('query') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = Math.min(
      parseInt(searchParams.get('pageSize') || PAGINATION_CONFIG.DEFAULT_PAGE_SIZE.toString()),
      PAGINATION_CONFIG.MAX_PAGE_SIZE
    )
    
    // Extraction des filtres
    const filters: SearchFilters = {
      query: query || undefined,
      type: searchParams.get('type')?.split(',') as any,
      location: searchParams.get('location') ? JSON.parse(searchParams.get('location')!) : undefined,
      dateRange: searchParams.get('dateRange') ? JSON.parse(searchParams.get('dateRange')!) : undefined,
      domain: searchParams.get('domain')?.split(','),
      level: searchParams.get('level')?.split(','),
      isRemote: searchParams.get('isRemote') === 'true' ? true : undefined,
      isFree: searchParams.get('isFree') === 'true' ? true : undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'relevance',
      sortOrder: (searchParams.get('sortOrder') as any) || 'desc'
    }

    // Récupération de tous les contenus
    let allContent = getAllContent()

    // Application des filtres
    let filteredContent = allContent.filter(item => {
      // Filtre par query (recherche textuelle)
      if (filters.query) {
        const searchText = filters.query.toLowerCase()
        const itemText = `${item.title} ${item.description}`.toLowerCase()
        if (!itemText.includes(searchText)) {
          return false
        }
      }

      // Filtre par type de contenu
      if (filters.type && filters.type.length > 0) {
        const itemType = getContentType(item)
        if (!filters.type.includes(itemType)) {
          return false
        }
      }

      // Filtre par localisation
      if (filters.location) {
        if (filters.location.city) {
          const cityMatch = item.location.city.toLowerCase().includes(filters.location.city.toLowerCase())
          if (!cityMatch) return false
        }
        if (filters.location.region) {
          if (item.location.region !== filters.location.region) return false
        }
      }

      // Filtre par domaine
      if (filters.domain && filters.domain.length > 0) {
        const itemDomain = getItemDomain(item)
        if (!itemDomain || !filters.domain.includes(itemDomain)) {
          return false
        }
      }

      // Filtre par niveau (pour les formations et stages)
      if (filters.level && filters.level.length > 0) {
        const itemLevel = getItemLevel(item)
        if (!itemLevel || !filters.level.includes(itemLevel)) {
          return false
        }
      }

      // Filtre télétravail
      if (filters.isRemote) {
        const isRemote = getItemRemoteStatus(item)
        if (!isRemote) return false
      }

      // Filtre gratuit
      if (filters.isFree) {
        const isFree = getItemFreeStatus(item)
        if (!isFree) return false
      }

      return true
    })

    // Tri des résultats
    filteredContent = sortContent(filteredContent, filters.sortBy, filters.sortOrder)

    // Pagination
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedContent = filteredContent.slice(startIndex, endIndex)

    // Construction de la réponse
    const result: SearchResult<ContentItem> = {
      items: paginatedContent,
      total: filteredContent.length,
      page,
      pageSize,
      hasMore: endIndex < filteredContent.length
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Erreur API de recherche:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

// Fonctions utilitaires
function getContentType(item: ContentItem): string {
  if ('type' in item && item.type) return 'formation'
  if ('company' in item) return 'stage'
  if ('organizer' in item && 'expectedCompanies' in item) return 'salon'
  return 'evenement'
}

function getItemDomain(item: ContentItem): string | undefined {
  if ('domain' in item) return item.domain
  return undefined
}

function getItemLevel(item: ContentItem): string | undefined {
  if ('level' in item) return item.level
  return undefined
}

function getItemRemoteStatus(item: ContentItem): boolean {
  if ('isOnline' in item) return item.isOnline
  if ('isRemote' in item) return item.isRemote
  if ('isVirtual' in item) return item.isVirtual
  return false
}

function getItemFreeStatus(item: ContentItem): boolean {
  if ('cost' in item) return item.cost === 0 || item.cost === undefined
  if ('salary' in item) return true // Les stages sont considérés comme "gratuits" pour l'étudiant
  if ('registrationFee' in item) return item.registrationFee === 0 || item.registrationFee === undefined
  if ('isFree' in item) return item.isFree
  if ('price' in item) return item.price === 0 || item.price === undefined
  return false
}

function sortContent(
  content: ContentItem[],
  sortBy: string = 'relevance',
  sortOrder: string = 'desc'
): ContentItem[] {
  const sorted = [...content].sort((a, b) => {
    let comparison = 0

    switch (sortBy) {
      case 'date':
        const dateA = getItemDate(a)
        const dateB = getItemDate(b)
        comparison = dateA.getTime() - dateB.getTime()
        break
      
      case 'title':
        comparison = a.title.localeCompare(b.title, 'fr')
        break
      
      case 'location':
        comparison = a.location.city.localeCompare(b.location.city, 'fr')
        break
      
      case 'relevance':
      default:
        // Tri par pertinence : plus récent d'abord
        comparison = b.updatedAt.getTime() - a.updatedAt.getTime()
        break
    }

    return sortOrder === 'asc' ? comparison : -comparison
  })

  return sorted
}

function getItemDate(item: ContentItem): Date {
  if ('startDate' in item) return item.startDate
  return item.createdAt
}
